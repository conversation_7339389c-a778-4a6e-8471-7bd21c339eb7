const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDatabaseSettings() {
  console.log('🧪 Testing Database Settings...');
  
  try {
    // Test 1: Count total settings
    console.log('\n📋 Test 1: Counting settings...');
    const totalCount = await prisma.appSettings.count();
    console.log(`✅ Total settings in database: ${totalCount}`);
    
    // Test 2: Get payments settings
    console.log('\n📋 Test 2: Getting payments settings...');
    const paymentsSettings = await prisma.appSettings.findMany({
      where: { category: 'payments' }
    });
    console.log(`✅ Found ${paymentsSettings.length} payments settings:`);
    paymentsSettings.forEach(setting => {
      console.log(`   ${setting.key}: ${setting.value} (${setting.type})`);
    });
    
    // Test 3: Get all categories
    console.log('\n📋 Test 3: Getting all categories...');
    const categories = await prisma.appSettings.groupBy({
      by: ['category'],
      _count: { category: true }
    });
    console.log('✅ Categories found:');
    categories.forEach(cat => {
      console.log(`   ${cat.category}: ${cat._count.category} settings`);
    });
    
    // Test 4: Test specific setting lookup
    console.log('\n📋 Test 4: Testing specific setting lookup...');
    const enabledSetting = await prisma.appSettings.findUnique({
      where: {
        category_key: {
          category: 'payments',
          key: 'enabled'
        }
      }
    });
    
    if (enabledSetting) {
      console.log(`✅ Found payments.enabled: ${enabledSetting.value} (${enabledSetting.type})`);
    } else {
      console.log('❌ payments.enabled setting not found');
    }
    
    console.log('\n✅ Database tests completed!');
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testDatabaseSettings().catch(console.error);
