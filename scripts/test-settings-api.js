const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Import the settings service
const { SettingsService } = require('../lib/settings/settings-service.ts');

async function testSettingsService() {
  console.log('🧪 Testing Settings Service...');
  
  try {
    const settingsService = new SettingsService();
    
    // Test 1: Get all settings
    console.log('\n📋 Test 1: Getting all settings...');
    const allSettings = await settingsService.getAllSettings();
    console.log('✅ All settings loaded successfully');
    console.log('Categories found:', Object.keys(allSettings));
    
    // Test 2: Get specific category
    console.log('\n📋 Test 2: Getting payments category...');
    const paymentsSettings = await settingsService.getCategorySettings('payments');
    console.log('✅ Payments settings:', paymentsSettings);
    
    // Test 3: Get specific setting
    console.log('\n📋 Test 3: Getting specific setting...');
    const enabled = await settingsService.getSetting('payments', 'enabled');
    console.log('✅ Payments enabled:', enabled);
    
    // Test 4: Update a setting
    console.log('\n📋 Test 4: Updating a setting...');
    await settingsService.updateSetting('payments', 'enabled', true, 'test-user');
    const updatedEnabled = await settingsService.getSetting('payments', 'enabled');
    console.log('✅ Updated payments enabled:', updatedEnabled);
    
    // Test 5: Update category settings
    console.log('\n📋 Test 5: Updating category settings...');
    await settingsService.updateCategorySettings('payments', {
      enabled: false,
      provider: 'disabled',
      testMode: true
    }, 'test-user');
    
    const updatedCategory = await settingsService.getCategorySettings('payments');
    console.log('✅ Updated category settings:', {
      enabled: updatedCategory.enabled,
      provider: updatedCategory.provider,
      testMode: updatedCategory.testMode
    });
    
    console.log('\n✅ All tests passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testSettingsService().catch(console.error);
