import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requirePermission, logActivity, validateRequestData } from '@/lib/user-management/middleware';
import { ACTIVITY_ACTIONS } from '@/lib/user-management/permissions';

// GET /api/admin/roles/[id] - Get specific role
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params;
  return requirePermission('roles', 'read')(request, async (req, user) => {
    try {
      const role = await prisma.role.findUnique({
        where: { id: params.id },
        include: {
          permissions: {
            include: {
              permission: true,
            },
          },
          _count: {
            select: {
              userRoles: true,
            },
          },
        },
      });

      if (!role) {
        return NextResponse.json(
          { error: 'Role not found' },
          { status: 404 }
        );
      }

      const formattedRole = {
        id: role.id,
        name: role.name,
        displayName: role.displayName,
        description: role.description,
        isSystem: role.isSystem,
        createdAt: role.createdAt,
        updatedAt: role.updatedAt,
        permissions: role.permissions.map(rp => rp.permission),
        userCount: role._count.userRoles,
      };

      await logActivity(
        user.id,
        ACTIVITY_ACTIONS.API_CALLED,
        'roles',
        role.id,
        { action: 'get' },
        req
      );

      return NextResponse.json({ role: formattedRole });
    } catch (error) {
      console.error('Error fetching role:', error);
      return NextResponse.json(
        { error: 'Failed to fetch role' },
        { status: 500 }
      );
    }
  });
}

// PUT /api/admin/roles/[id] - Update role
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return requirePermission('roles', 'update')(request, async (req, user) => {
    try {
      const body = await req.json();

      const validation = validateRequestData(
        body,
        ['name', 'displayName'],
        ['description', 'permissions']
      );

      if (!validation.isValid) {
        return NextResponse.json(
          { error: 'Validation failed', details: validation.errors },
          { status: 400 }
        );
      }

      const { name, displayName, description, permissions } = validation.validatedData!;

      // Check if role exists
      const existingRole = await prisma.role.findUnique({
        where: { id: params.id },
      });

      if (!existingRole) {
        return NextResponse.json(
          { error: 'Role not found' },
          { status: 404 }
        );
      }

      // Check if it's a system role - only super admin can modify system roles
      if (existingRole.isSystem && !user.isSuperAdmin) {
        return NextResponse.json(
          { error: 'Only super administrators can modify system roles' },
          { status: 403 }
        );
      }

      // Check if name is being changed and if new name already exists
      if (name !== existingRole.name) {
        const nameExists = await prisma.role.findUnique({
          where: { name: name as string },
        });

        if (nameExists) {
          return NextResponse.json(
            { error: 'Role with this name already exists' },
            { status: 400 }
          );
        }
      }

      // Update role
      const updatedRole = await prisma.role.update({
        where: { id: params.id },
        data: {
          name: name as string,
          displayName: displayName as string,
          description: description as string,
        },
      });

      // Update permissions if provided
      if (permissions && Array.isArray(permissions)) {
        // Remove existing permissions
        await prisma.rolePermission.deleteMany({
          where: { roleId: params.id },
        });

        // Add new permissions
        for (const permissionId of permissions) {
          await prisma.rolePermission.create({
            data: {
              roleId: params.id,
              permissionId: permissionId as string,
            },
          });
        }
      }

      await logActivity(
        user.id,
        ACTIVITY_ACTIONS.ROLE_UPDATED,
        'roles',
        updatedRole.id,
        {
          name: updatedRole.name,
          displayName: updatedRole.displayName,
          permissions: permissions || [],
        },
        req
      );

      return NextResponse.json({
        message: 'Role updated successfully',
        role: {
          id: updatedRole.id,
          name: updatedRole.name,
          displayName: updatedRole.displayName,
          description: updatedRole.description,
          updatedAt: updatedRole.updatedAt,
        },
      });
    } catch (error) {
      console.error('Error updating role:', error);
      return NextResponse.json(
        { error: 'Failed to update role' },
        { status: 500 }
      );
    }
  });
}

// DELETE /api/admin/roles/[id] - Delete role
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return requirePermission('roles', 'delete')(request, async (req, user) => {
    try {
      // Check if role exists
      const existingRole = await prisma.role.findUnique({
        where: { id: params.id },
        include: {
          _count: {
            select: {
              userRoles: true,
            },
          },
        },
      });

      if (!existingRole) {
        return NextResponse.json(
          { error: 'Role not found' },
          { status: 404 }
        );
      }

      // Check if it's a system role - only super admin can delete system roles
      if (existingRole.isSystem && !user.isSuperAdmin) {
        return NextResponse.json(
          { error: 'Only super administrators can delete system roles' },
          { status: 403 }
        );
      }

      // Check if role is assigned to users
      if (existingRole._count.userRoles > 0) {
        return NextResponse.json(
          { error: 'Cannot delete role that is assigned to users' },
          { status: 400 }
        );
      }

      // Delete role permissions first
      await prisma.rolePermission.deleteMany({
        where: { roleId: params.id },
      });

      // Delete role
      await prisma.role.delete({
        where: { id: params.id },
      });

      await logActivity(
        user.id,
        ACTIVITY_ACTIONS.ROLE_DELETED,
        'roles',
        params.id,
        {
          name: existingRole.name,
          displayName: existingRole.displayName,
        },
        req
      );

      return NextResponse.json({
        message: 'Role deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting role:', error);
      return NextResponse.json(
        { error: 'Failed to delete role' },
        { status: 500 }
      );
    }
  });
}
