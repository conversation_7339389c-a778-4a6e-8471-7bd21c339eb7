"use client";

import React, { use<PERSON><PERSON>back, useState, useRef, useEffect, useMemo } from "react";
import React<PERSON>low, {
  Background,
  Controls,
  addEdge,
  useEdgesState,
  useNodesState,
  Connection,
  ConnectionMode,
  Edge,
  NodeTypes,
  Node,
  Panel,
  BackgroundVariant,
  SelectionMode,
} from "reactflow";
import "reactflow/dist/style.css";
import "./workflow-styles.css";
import { useTheme } from "@/components/theme-provider";

// Define interfaces for node data
interface NodeData {
  label: string;
  value?: string;
  inputValue?: string;
  fileContent?: string;
  onChange?: (value: string) => void;
  onRawDataChange?: (value: string) => void;
  onJsonChange?: (value: string) => void;
}

// Define the workflow data interface
interface WorkflowData {
  id: string;
  name: string;
  description: string | null;
  nodes: any[];
  edges: any[];
  createdAt: string;
  updatedAt: string;
  userId: string;
}

import NodeSelector from "./node-selector";
import WorkflowControls from "./workflow-controls";
import WorkflowTemplates from "./workflow-templates";
import { useCanvasNodes } from "@/hooks/use-canvas-nodes";
import { ExecutionProvider } from "./execution-context";
import { ExecutionPanel } from "./execution-panel";

// Import the new modular node system
import { nodeRegistry } from "@/lib/workflow/node-registry";
import { registerAllNodes } from "./nodes";
import {
  createNewNode,
  processWorkflowNodes,
  sanitizeNodesForSave,
  generateRandomPosition
} from "@/lib/workflow/node-utils";
import { NodeDefinition } from "@/lib/node-loader";

// Dynamic node types - will be populated from registry
const nodeTypes: NodeTypes = {};

// Helper function to determine if a node type needs an onChange handler (now uses registry)
const needsOnChangeHandler = (nodeType: string | undefined): boolean => {
  if (!nodeType) return false;
  const metadata = nodeRegistry.getNodeMetadata(nodeType);
  return metadata?.needsOnChangeHandler || false;
};

// We start with empty nodes and edges for a blank canvas

interface WorkflowContainerProps {
  initialWorkflow?: WorkflowData | null;
}

export default function WorkflowContainer({ initialWorkflow }: WorkflowContainerProps) {
  // State to track if node registry is ready
  const [isNodeRegistryReady, setIsNodeRegistryReady] = useState(false);

  // Use canvas nodes hook for enhanced node management
  const {
    allNodes: canvasNodes,
    refreshNodes,
    loading: canvasNodesLoading
  } = useCanvasNodes();

  // Initialize node registry and populate nodeTypes synchronously
  useEffect(() => {
    const initializeNodeRegistry = async () => {
      try {
        // Register all nodes if not already done
        if (nodeRegistry.getAllNodes().length === 0) {
          registerAllNodes();
        }

        // Populate nodeTypes object dynamically
        const allNodes = nodeRegistry.getAllNodes();
        console.log(`Loading ${allNodes.length} node types...`);

        // Load all node components
        const loadPromises = allNodes.map(async (nodeMetadata) => {
          try {
            const component = await nodeRegistry.getNodeComponent(nodeMetadata.type);
            if (component) {
              nodeTypes[nodeMetadata.type] = component;
              console.log(`Loaded node type: ${nodeMetadata.type}`);
            }
          } catch (error) {
            console.error(`Failed to load component for node type ${nodeMetadata.type}:`, error);
          }
        });

        // Wait for all components to load
        await Promise.all(loadPromises);

        console.log(`Node registry ready with ${Object.keys(nodeTypes).length} node types`);
        setIsNodeRegistryReady(true);
      } catch (error) {
        console.error("Error initializing node registry:", error);
        setIsNodeRegistryReady(true); // Set to true anyway to prevent infinite loading
      }
    };

    initializeNodeRegistry();
  }, []);

  // Add error boundary to catch any remaining errors
  try {
    console.log("WorkflowContainer initializing with initialWorkflow:", initialWorkflow);

    const [workflowId, setWorkflowId] = useState<string | null>(initialWorkflow?.id || null);
    const [workflowName, setWorkflowName] = useState<string>(initialWorkflow?.name || "");
    const [workflowDescription, setWorkflowDescription] = useState<string | null>(initialWorkflow?.description || null);
    const [error, setError] = useState<Error | null>(null);

  // Create initial empty arrays for nodes and edges
  const [nodes, setNodes, onNodesChange] = useNodesState<NodeData>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  // Initialize nodes and edges from initialWorkflow - ultra-safe version
  useEffect(() => {
    if (!initialWorkflow) return;

    try {
      console.log("Initializing workflow with data:", initialWorkflow);

      // Process nodes using the new utility function
      let processedNodes: Node[] = [];
      try {
        if (initialWorkflow.nodes && Array.isArray(initialWorkflow.nodes)) {
          processedNodes = processWorkflowNodes(initialWorkflow.nodes, {
            onChange: handleInputChange,
            onRawDataChange: handleRawDataChange,
            onJsonChange: handleJsonChange
          });
        }
      } catch (err) {
        console.error("Error processing nodes:", err);
        processedNodes = [];
      }

      // Process edges with maximum safety
      let processedEdges: Edge[] = [];
      try {
        if (initialWorkflow.edges && Array.isArray(initialWorkflow.edges)) {
          processedEdges = initialWorkflow.edges
            .filter(edge => {
              try {
                return edge &&
                       typeof edge === 'object' &&
                       edge.source &&
                       typeof edge.source === 'string' &&
                       edge.target &&
                       typeof edge.target === 'string';
              } catch {
                return false;
              }
            })
            .map(edge => {
              try {
                return {
                  id: edge.id || `e${edge.source}-${edge.target}`,
                  source: String(edge.source),
                  target: String(edge.target),
                  style: { strokeWidth: 3, stroke: '#666666' },
                  animated: true
                };
              } catch {
                return null;
              }
            })
            .filter(edge => edge !== null);
        }
      } catch (err) {
        console.error("Error processing edges:", err);
        processedEdges = [];
      }

      // Set nodes and edges safely
      try {
        setNodes(processedNodes);
      } catch (err) {
        console.error("Error setting nodes:", err);
        setNodes([]);
      }

      try {
        setEdges(processedEdges);
      } catch (err) {
        console.error("Error setting edges:", err);
        setEdges([]);
      }

    } catch (err) {
      console.error("Error initializing workflow:", err);
      setError(err instanceof Error ? err : new Error("Failed to initialize workflow"));
      // Reset to empty state on error
      setNodes([]);
      setEdges([]);
    }
  }, [initialWorkflow]);

  // Update nodes when input changes - fixed scoping
  const handleInputChange = useCallback((value: string, sourceNodeId?: string) => {
    console.log("handleInputChange called", { value, sourceNodeId });

    setNodes(prevNodes => {
      const nodesCopy = [...prevNodes];

      // Update source node if provided
      if (sourceNodeId) {
        const sourceIndex = nodesCopy.findIndex(node => node?.id === sourceNodeId);
        if (sourceIndex >= 0 && nodesCopy[sourceIndex]) {
          const sourceNode = nodesCopy[sourceIndex];
          if (sourceNode.type === 'textInput' || sourceNode.type === 'numberInput') {
            nodesCopy[sourceIndex] = {
              ...sourceNode,
              data: { ...sourceNode.data, value }
            };
          }
        }
      } else {
        // Legacy: update all output nodes
        for (let i = 0; i < nodesCopy.length; i++) {
          const node = nodesCopy[i];
          if (node?.type && ['textOutput', 'chartOutput', 'tableOutput'].includes(node.type)) {
            nodesCopy[i] = {
              ...node,
              data: { ...node.data, inputValue: value }
            };
          }
        }
      }

      return nodesCopy;
    });

    // Update connected target nodes if we have a source node
    if (sourceNodeId) {
      setEdges(currentEdges => {
        const sourceConnections = currentEdges.filter(edge => edge?.source === sourceNodeId);
        const targetNodeIds = new Set(sourceConnections.map(edge => edge.target));

        console.log(`handleInputChange: Found ${sourceConnections.length} connections from ${sourceNodeId}`, sourceConnections);
        console.log(`handleInputChange: Target node IDs:`, Array.from(targetNodeIds));

        if (targetNodeIds.size > 0) {
          setNodes(currentNodes => {
            const nodesCopy = [...currentNodes];

            for (let i = 0; i < nodesCopy.length; i++) {
              const node = nodesCopy[i];
              if (node?.id && targetNodeIds.has(node.id)) {
                console.log(`handleInputChange: Updating target node ${node.id} with value:`, value);
                nodesCopy[i] = {
                  ...node,
                  data: { ...node.data, inputValue: value }
                };
              }
            }

            return nodesCopy;
          });
        }

        return currentEdges;
      });
    }
  }, []);

  // Handle raw data input changes from generate-text-node's second output
  const handleRawDataChange = useCallback((value: string, sourceNodeId: string) => {
    try {
      console.log(`handleRawDataChange called with value: "${value}" from source: ${sourceNodeId}`);

      setEdges(currentEdges => {
        // Find connections from rawData handle
        const sourceConnections = currentEdges.filter(edge =>
          edge?.source === sourceNodeId && edge?.sourceHandle === 'rawData'
        );
        const targetNodeIds = new Set(sourceConnections.map(edge => edge.target));

        if (targetNodeIds.size > 0) {
          setNodes(currentNodes => {
            const nodesCopy = [...currentNodes];

            // Update target nodes
            for (let i = 0; i < nodesCopy.length; i++) {
              const node = nodesCopy[i];
              if (node?.id && targetNodeIds.has(node.id)) {
                nodesCopy[i] = {
                  ...node,
                  data: { ...node.data, inputValue: value }
                };
              }
            }

            return nodesCopy;
          });
        }

        return currentEdges;
      });
    } catch (error) {
      console.error("Error in handleRawDataChange:", error);
    }
  }, []);

  // Handle JSON output changes from converter node's second output
  const handleJsonChange = useCallback((value: string, sourceNodeId: string) => {
    try {
      console.log(`handleJsonChange called with value: "${value}" from source: ${sourceNodeId}`);

      setEdges(currentEdges => {
        // Find connections from jsonOutput handle
        const sourceConnections = currentEdges.filter(edge =>
          edge?.source === sourceNodeId && edge?.sourceHandle === 'jsonOutput'
        );
        const targetNodeIds = new Set(sourceConnections.map(edge => edge.target));

        if (targetNodeIds.size > 0) {
          setNodes(currentNodes => {
            const nodesCopy = [...currentNodes];

            // Update target nodes
            for (let i = 0; i < nodesCopy.length; i++) {
              const node = nodesCopy[i];
              if (node?.id && targetNodeIds.has(node.id)) {
                nodesCopy[i] = {
                  ...node,
                  data: { ...node.data, inputValue: value }
                };
              }
            }

            return nodesCopy;
          });
        }

        return currentEdges;
      });
    } catch (error) {
      console.error("Error in handleJsonChange:", error);
    }
  }, []);

  // Track selected elements with useRef to avoid re-renders when selection changes
  const selectedElementsRef = useRef<{ nodes: Node[]; edges: Edge[] }>({
    nodes: [],
    edges: [],
  });

  // Use state only for components that need to re-render on selection change
  const [selectedElements, setSelectedElements] = useState<{ nodes: Node[]; edges: Edge[] }>({
    nodes: [],
    edges: [],
  });

  // Handle node selection - optimized for performance
  const onSelectionChange = useCallback(
    ({ nodes, edges }: { nodes: Node[]; edges: Edge[] }) => {
      // Store selection in ref for quick access without re-renders
      selectedElementsRef.current = { nodes, edges };

      // Only update state if the selection has actually changed
      // Use shallow comparison to avoid unnecessary re-renders
      setSelectedElements(prev => {
        if (
          prev.nodes.length !== nodes.length ||
          prev.edges.length !== edges.length ||
          nodes.some((node, i) => prev.nodes[i]?.id !== node.id) ||
          edges.some((edge, i) => prev.edges[i]?.id !== edge.id)
        ) {
          // Update edge styles when selected
          if (edges && edges.length > 0) {
            setEdges(eds =>
              eds.map(edge => {
                if (edges.some(selectedEdge => selectedEdge.id === edge.id)) {
                  return {
                    ...edge,
                    style: {
                      ...edge.style,
                      stroke: '#ff0000',
                      strokeWidth: 5,
                    },
                    animated: true,
                  };
                } else {
                  // Reset non-selected edges
                  return {
                    ...edge,
                    style: {
                      ...edge.style,
                      stroke: '#666666',
                      strokeWidth: 3,
                    },
                  };
                }
              })
            );
          }

          return { nodes, edges };
        }
        return prev;
      });
    },
    [setEdges]
  );

  // Handle connections between nodes - optimized for performance and reliability
  const onConnect = useCallback(
    (connection: Connection) => {
      try {
        // Skip if connection is invalid
        if (!connection || !connection.source || !connection.target) {
          console.log("Skipping invalid connection:", connection);
          return;
        }

        // Create the edge with a unique ID to avoid duplicates
        const newEdge = {
          ...connection,
          id: `e${connection.source}-${connection.target}-${Date.now()}`,
          style: {
            strokeWidth: 3,
            stroke: '#666666',
          },
          animated: true,
        };

        // Add the edge safely
        try {
          setEdges(eds => {
            try {
              return addEdge(newEdge, eds);
            } catch (error) {
              console.error("Error adding edge:", error);
              return eds; // Return unchanged on error
            }
          });
        } catch (error) {
          console.error("Error in setEdges:", error);
        }

        // Use a function to update target nodes after connection
        const updateTargetNode = () => {
          try {
            // Find source and target nodes from current state
            setNodes(currentNodes => {
              try {
                // Validate nodes array
                if (!Array.isArray(currentNodes)) {
                  console.error("currentNodes is not an array:", currentNodes);
                  return currentNodes;
                }

                // Find source and target nodes
                const sourceNode = currentNodes.find(node => node && node.id === connection.source);
                const targetNode = currentNodes.find(node => node && node.id === connection.target);

                // Skip if either node is missing or invalid
                if (!sourceNode || !targetNode || !sourceNode.data || !targetNode.data) {
                  console.log("Missing or invalid nodes:", { sourceNode, targetNode });
                  return currentNodes;
                }

                let sourceValue = '';

                try {
                  // Get the appropriate value based on node type
                  if (sourceNode.type === 'textInput') {
                    // For text input nodes, use the value from the node's data
                    sourceValue = sourceNode.data.value || '';
                  } else if (sourceNode.type === 'numberInput') {
                    // For number input nodes, use the value from the node's data
                    sourceValue = sourceNode.data.value || '0';
                  } else if (sourceNode.type === 'fileInput') {
                    // For file input nodes, check if there's content
                    sourceValue = sourceNode.data.fileContent || '';
                  } else if (sourceNode.type === 'mathOperation') {
                    // For math operation nodes, use the output value
                    sourceValue = sourceNode.data.inputValue || '';
                  } else {
                    // For other nodes, use the inputValue if available
                    sourceValue = sourceNode.data.inputValue || '';
                  }
                } catch (error) {
                  console.error("Error getting source value:", error);
                  sourceValue = '';
                }

                // Update the target node with this value
                return currentNodes.map(node => {
                  try {
                    if (!node || !node.data) return node;

                    if (node.id === targetNode.id) {
                      console.log(`Updating target node ${node.id} of type ${node.type} with value:`, sourceValue);

                      // Create a new data object with the updated inputValue
                      const updatedData = {
                        ...node.data,
                        inputValue: sourceValue,
                      };

                      // For math operation nodes, ensure we preserve the onChange handler
                      if (node.type === 'mathOperation' && !updatedData.onChange && node.data.onChange) {
                        updatedData.onChange = node.data.onChange;
                      }

                      return {
                        ...node,
                        data: updatedData,
                      };
                    }
                    return node;
                  } catch (error) {
                    console.error(`Error updating node ${node?.id}:`, error);
                    return node; // Return the original node on error
                  }
                });
              } catch (error) {
                console.error("Error in node update function:", error);
                return currentNodes; // Return the original nodes on error
              }
            });
          } catch (error) {
            console.error("Error in updateTargetNode:", error);
          }
        };

        // Use requestAnimationFrame for immediate updates
        requestAnimationFrame(updateTargetNode);
      } catch (error) {
        console.error("Error in onConnect:", error);
      }
    },
    [setEdges, setNodes]
  );

  // Add a new node to the workflow - now supports both built-in and installed nodes
  const handleAddNode = useCallback(
    (nodeType: string, nodeDefinition?: NodeDefinition) => {
      try {
        // Validate node type
        if (!nodeType || typeof nodeType !== 'string') {
          console.error("Invalid node type:", nodeType);
          return;
        }

        console.log(`Adding new node of type: ${nodeType}`, nodeDefinition ? 'from marketplace' : 'built-in');

        // Generate a unique ID first
        const nodeId = `node-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

        let newNode;

        if (nodeDefinition) {
          // This is an installed node from the marketplace
          console.log(`Creating installed node: ${nodeDefinition.name}`);

          // Create a custom node for installed marketplace nodes
          newNode = {
            id: nodeId,
            type: 'installed-node', // Use a special type for installed nodes
            position: generateRandomPosition(),
            data: {
              label: nodeDefinition.name,
              description: nodeDefinition.description,
              nodeDefinition: nodeDefinition,
              nodeType: nodeType,
              version: nodeDefinition.version,
              category: nodeDefinition.category,
              icon: nodeDefinition.icon,
              inputs: nodeDefinition.inputs,
              outputs: nodeDefinition.outputs,
              // Add handlers
              onChange: (value: string) => handleInputChange(value, nodeId),
              onRawDataChange: (value: string) => handleRawDataChange(value, nodeId),
              onJsonChange: (value: string) => handleJsonChange(value, nodeId)
            }
          };
        } else {
          // This is a built-in node
          newNode = createNewNode(
            nodeType,
            generateRandomPosition(),
            {
              onChange: (value: string) => handleInputChange(value, nodeId),
              onRawDataChange: (value: string) => handleRawDataChange(value, nodeId),
              onJsonChange: (value: string) => handleJsonChange(value, nodeId)
            },
            nodeId // Pass the custom ID
          );
        }

        // Add the node to the workflow
        setNodes(prevNodes => {
          try {
            return [...prevNodes, newNode];
          } catch (error) {
            console.error("Error adding new node:", error);
            return prevNodes; // Return unchanged on error
          }
        });
      } catch (error) {
        console.error("Error in handleAddNode:", error);
      }
    },
    [handleInputChange, handleRawDataChange, handleJsonChange]
  );

  // Removed dynamic import useEffect to prevent errors
  // Node addition is now handled directly through props or other means

  // Update workflow details (name and description)
  const updateWorkflowDetails = async (name: string, description?: string) => {
    if (!workflowId) return false;

    try {
      // Update local state
      setWorkflowName(name);
      setWorkflowDescription(description || null);

      // Create a clean copy of nodes without function handlers using utility
      const cleanNodes = sanitizeNodesForSave(nodes);

      // Prepare the request data
      const workflowData = {
        name,
        description,
        nodes: JSON.stringify(cleanNodes),
        edges: JSON.stringify(edges)
      };

      const response = await fetch(`/api/workflow/${workflowId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(workflowData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update workflow details');
      }

      return true;
    } catch (error) {
      console.error('Error updating workflow details:', error);
      return false;
    }
  };

  // Save workflow to database
  const handleSaveWorkflow = async (name: string, nodes: Node[], edges: Edge[], description?: string) => {
    console.log('Saving workflow to database:', name, description);

    try {
      // Update local state
      setWorkflowName(name);
      setWorkflowDescription(description || null);

      // Create a clean copy of nodes without function handlers using utility
      const cleanNodes = sanitizeNodesForSave(nodes);

      // Prepare the request data
      const workflowData = {
        name,
        description,
        nodes: JSON.stringify(cleanNodes),
        edges: JSON.stringify(edges)
      };

      let response;

      // If we have a workflow ID, update the existing workflow
      if (workflowId) {
        response = await fetch(`/api/workflow/${workflowId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(workflowData),
        });
      } else {
        // Otherwise, create a new workflow
        response = await fetch('/api/workflow', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(workflowData),
        });
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save workflow');
      }

      const savedWorkflow = await response.json();

      // Update the workflow ID if this is a new workflow
      if (!workflowId) {
        setWorkflowId(savedWorkflow.id);

        // Update the URL without refreshing the page
        window.history.pushState({}, '', `/workflow/${savedWorkflow.id}`);
      }

      return true;
    } catch (error) {
      console.error('Error saving workflow:', error);
      return false;
    }
  };

  // Load workflow - completely rewritten for maximum reliability
  const handleLoadWorkflow = (workflow: { name: string; nodes: Node[]; edges: Edge[] }) => {
    try {
      // Validate input
      if (!workflow || !Array.isArray(workflow.nodes) || !Array.isArray(workflow.edges)) {
        console.error("Invalid workflow data:", workflow);
        return;
      }

      // Create a stable reference to handleInputChange
      const stableHandleInputChange = handleInputChange;

      // Process nodes to ensure they have the correct structure and handlers
      const processedNodes = workflow.nodes
        .filter(node => !!node) // Filter out null or undefined nodes
        .map(node => {
          try {
            // Create a base node with all required properties
            const baseNode = {
              ...node,
              // Ensure ID exists
              id: node.id || `node-${Date.now()}-${Math.floor(Math.random() * 10000)}`,
              // Ensure type exists
              type: node.type || 'default',
              // Ensure position is valid
              position: (node.position &&
                        typeof node.position === 'object' &&
                        typeof node.position.x === 'number' &&
                        typeof node.position.y === 'number' &&
                        !isNaN(node.position.x) &&
                        !isNaN(node.position.y))
                ? { x: node.position.x, y: node.position.y } // Create a new position object
                : { x: Math.random() * 300 + 50, y: Math.random() * 300 + 50 },
              // Ensure data exists
              data: (node.data && typeof node.data === 'object')
                ? { ...node.data } // Create a new data object
                : { label: node.type || 'Node' }
            };

            // Add onChange handler for input nodes
            if (baseNode.type === 'textInput' || baseNode.type === 'numberInput') {
              baseNode.data.onChange = (value: string) => stableHandleInputChange(value, baseNode.id);
            }

            // Add special handlers for specific node types
            if (baseNode.type === 'generateText') {
              baseNode.data.onChange = (value: string) => stableHandleInputChange(value, baseNode.id);
              baseNode.data.onRawDataChange = (value: string) => handleRawDataChange(value, baseNode.id);
            }

            if (baseNode.type === 'converter') {
              baseNode.data.onChange = (value: string) => stableHandleInputChange(value, baseNode.id);
              baseNode.data.onJsonChange = (value: string) => handleJsonChange(value, baseNode.id);
            }

            // Add onChange handler for other nodes that need it
            if (typeof needsOnChangeHandler === 'function' &&
                needsOnChangeHandler(baseNode.type) &&
                baseNode.type !== 'textInput' &&
                baseNode.type !== 'numberInput' &&
                baseNode.type !== 'generateText' &&
                baseNode.type !== 'converter') {
              baseNode.data.onChange = (value: string) => stableHandleInputChange(value, baseNode.id);
            }

            return baseNode;
          } catch (error) {
            console.error("Error processing node:", error);
            // Return a default node if there's an error
            return {
              id: `node-${Date.now()}-${Math.floor(Math.random() * 10000)}`,
              type: 'default',
              position: { x: Math.random() * 300 + 50, y: Math.random() * 300 + 50 },
              data: { label: 'Error Node' }
            };
          }
        });

      // Process edges to ensure they have valid properties
      const processedEdges = workflow.edges
        .filter(edge => !!edge && edge.source && edge.target) // Filter out invalid edges
        .map(edge => ({
          ...edge,
          id: edge.id || `e${edge.source}-${edge.target}-${Date.now()}`,
          // Ensure source and target exist
          source: edge.source || '',
          target: edge.target || '',
          // Add default style
          style: {
            strokeWidth: 3,
            stroke: '#666666',
          },
          animated: true
        }));

      // Update the nodes and edges
      setNodes(processedNodes);
      setEdges(processedEdges);
    } catch (error) {
      console.error("Error loading workflow:", error);
      // If there's an error, reset to empty nodes and edges
      setNodes([]);
      setEdges([]);
    }
  };

  // Clear workflow
  const handleClearWorkflow = () => {
    setNodes([]);
    setEdges([]);
  };

  // Delete selected elements
  const handleDeleteSelected = () => {
    // Delete selected nodes
    if (selectedElements.nodes.length > 0) {
      const selectedNodeIds = selectedElements.nodes.map(node => node.id);
      setNodes(nodes.filter(node => !selectedNodeIds.includes(node.id)));

      // Also delete any edges connected to these nodes
      setEdges(edges.filter(
        edge => !selectedNodeIds.includes(edge.source) && !selectedNodeIds.includes(edge.target)
      ));
    }

    // Delete selected edges
    if (selectedElements.edges.length > 0) {
      const selectedEdgeIds = selectedElements.edges.map(edge => edge.id);
      setEdges(edges.filter(edge => !selectedEdgeIds.includes(edge.id)));
    }
  };

  // Load template - rewritten for maximum reliability
  const handleLoadTemplate = (templateNodes: Node[], templateEdges: Edge[]) => {
    try {
      // Validate input
      if (!Array.isArray(templateNodes) || !Array.isArray(templateEdges)) {
        console.error("Invalid template data:", { templateNodes, templateEdges });
        return;
      }

      // Create a stable reference to handleInputChange
      const stableHandleInputChange = handleInputChange;

      // Process nodes to add onChange handlers where needed
      const processedNodes = templateNodes
        .filter(node => !!node) // Filter out null or undefined nodes
        .map(node => {
          try {
            // Create a base node with all required properties
            const baseNode = {
              ...node,
              // Ensure ID exists
              id: node.id || `node-${Date.now()}-${Math.floor(Math.random() * 10000)}`,
              // Ensure type exists
              type: node.type || 'default',
              // Ensure position is valid
              position: (node.position &&
                        typeof node.position === 'object' &&
                        typeof node.position.x === 'number' &&
                        typeof node.position.y === 'number' &&
                        !isNaN(node.position.x) &&
                        !isNaN(node.position.y))
                ? { x: node.position.x, y: node.position.y } // Create a new position object
                : { x: Math.random() * 300 + 50, y: Math.random() * 300 + 50 },
              // Ensure data exists
              data: (node.data && typeof node.data === 'object')
                ? { ...node.data } // Create a new data object
                : { label: node.type || 'Node' }
            };

            // Add onChange handler if needed
            if (baseNode.type === 'textInput' || baseNode.type === 'numberInput') {
              baseNode.data.onChange = (value: string) => stableHandleInputChange(value, baseNode.id);
            } else if (baseNode.type === 'generateText') {
              baseNode.data.onChange = (value: string) => stableHandleInputChange(value, baseNode.id);
              baseNode.data.onRawDataChange = (value: string) => handleRawDataChange(value, baseNode.id);
            } else if (baseNode.type === 'converter') {
              baseNode.data.onChange = (value: string) => stableHandleInputChange(value, baseNode.id);
              baseNode.data.onJsonChange = (value: string) => handleJsonChange(value, baseNode.id);
            } else if (typeof needsOnChangeHandler === 'function' && needsOnChangeHandler(baseNode.type)) {
              baseNode.data.onChange = (value: string) => stableHandleInputChange(value, baseNode.id);
            }

            return baseNode;
          } catch (error) {
            console.error("Error processing template node:", error);
            // Return a default node if there was an error
            return {
              id: `node-${Date.now()}-${Math.floor(Math.random() * 10000)}`,
              type: 'default',
              position: { x: Math.random() * 300 + 50, y: Math.random() * 300 + 50 },
              data: { label: 'Error Node' }
            };
          }
        });

      // Process edges to ensure they have valid properties
      const processedEdges = templateEdges
        .filter(edge => !!edge && edge.source && edge.target) // Filter out invalid edges
        .map(edge => ({
          ...edge,
          id: edge.id || `e${edge.source}-${edge.target}-${Date.now()}`,
          // Ensure source and target exist
          source: edge.source || '',
          target: edge.target || ''
        }));

      // Update the nodes and edges
      setNodes(processedNodes);
      setEdges(processedEdges);
    } catch (error) {
      console.error("Error loading template:", error);
    }
  };

  // Performance optimization - track node count for potential optimizations
  const nodeCountRef = useRef(nodes.length);

  // Update node count reference when nodes change
  useEffect(() => {
    nodeCountRef.current = nodes.length;
  }, [nodes.length]);

  // Get theme information for styling
  const { isDarkTheme } = useTheme();

  // Removed duplicate initialization useEffect to prevent conflicts

  // Ensure nodes and edges are valid before rendering
  const validNodes = useMemo(() => {
    try {
      return nodes.filter(node =>
        node &&
        typeof node === 'object' &&
        node.id &&
        node.type &&
        node.position &&
        typeof node.position.x === 'number' &&
        typeof node.position.y === 'number' &&
        node.data
      );
    } catch (error) {
      console.error("Error filtering nodes:", error);
      return [];
    }
  }, [nodes]);

  const validEdges = useMemo(() => {
    try {
      return edges.filter(edge =>
        edge &&
        typeof edge === 'object' &&
        edge.id &&
        edge.source &&
        edge.target
      );
    } catch (error) {
      console.error("Error filtering edges:", error);
      return [];
    }
  }, [edges]);

  // Show loading state while node registry is initializing
  if (!isNodeRegistryReady) {
    return (
      <div className={`flex items-center justify-center h-screen w-full ${
        isDarkTheme ? 'bg-stone-900' : 'bg-stone-50'
      } transition-colors`}>
        <div className="flex flex-col items-center gap-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          <p className={`text-lg font-medium ${
            isDarkTheme ? 'text-stone-300' : 'text-stone-600'
          }`}>
            Loading Node Components...
          </p>
        </div>
      </div>
    );
  }

  // Display error if there is one
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen w-full bg-destructive/10">
        <div className="bg-destructive/10 border border-destructive text-destructive px-6 py-4 rounded-md max-w-md text-center">
          <h2 className="font-semibold mb-2">Error in Workflow Canvas</h2>
          <p>{error.message}</p>
          <p className="mt-2 text-sm text-muted-foreground">Please try refreshing the page or contact support if the issue persists.</p>
        </div>
      </div>
    );
  }

  try {
    return (
      <ExecutionProvider
        workflowId={workflowId || 'temp-workflow'}
        userId={initialWorkflow?.userId || 'temp-user'}
      >
        <div className="workflow-container" style={{ width: '100%', height: '100%', position: 'absolute' }}>
          <ReactFlow
          key="react-flow-instance"
          nodes={validNodes}
          edges={validEdges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onSelectionChange={onSelectionChange}
          nodeTypes={nodeTypes}
          fitView
          proOptions={{
            hideAttribution: true,
            account: 'paid-pro'
          }}
          nodesDraggable
          elementsSelectable
          selectNodesOnDrag
          multiSelectionKeyCode="Control"
          className={isDarkTheme ? "bg-stone-900" : "bg-stone-50"}
          style={{
            height: '100%',
            width: '100%',
            position: 'absolute',
            transition: 'background-color 0.3s ease'
          }}
          defaultEdgeOptions={{
            zIndex: 0
          }}
          disableKeyboardA11y={false}
          minZoom={0.2}
          maxZoom={2}
          defaultViewport={{ x: 0, y: 0, zoom: 1 }}
          elevateEdgesOnSelect={true}
          zoomOnScroll={true}
          panOnScroll={false}
          panOnDrag={true}
          selectionOnDrag={false}
          selectionMode={SelectionMode.Partial}
          onPaneClick={(event) => {
            try {
              // Prevent clicks on the pane from interfering with controls
              if ((event.target as HTMLElement).closest('.workflow-controls')) {
                event.stopPropagation();
              }
            } catch (err) {
              console.error("Error in onPaneClick:", err);
              setError(err instanceof Error ? err : new Error("Error in workflow canvas interaction"));
            }
          }}
          snapToGrid={true}
          snapGrid={[16, 16]}
          connectionMode={ConnectionMode.Loose}
          deleteKeyCode="Delete"
          selectionKeyCode="Shift"
          zoomActivationKeyCode="Meta"
          panActivationKeyCode="Space"
          zoomOnPinch={true}
          onlyRenderVisibleElements={false}
        >
        <Background
          color={isDarkTheme ? "rgba(255, 255, 255, 0.15)" : "rgba(0, 0, 0, 0.15)"}
          gap={16}
          size={1}
          variant={BackgroundVariant.Dots}
        />
        <Controls className="bg-background border shadow-sm rounded-md transition-colors" showInteractive={false} />

        <Panel position="top-left" className="bg-background/50 backdrop-blur-sm p-2 rounded-md shadow-sm transition-colors pointer-events-auto">
          <div className="flex gap-2 pointer-events-auto">
            <WorkflowTemplates onSelectTemplate={handleLoadTemplate} />
            <NodeSelector onAddNode={handleAddNode} />
          </div>
        </Panel>
        <WorkflowControls
          nodes={nodes}
          edges={edges}
          onSave={handleSaveWorkflow}
          onLoad={handleLoadWorkflow}
          onClear={handleClearWorkflow}
          onDeleteSelected={handleDeleteSelected}
          onUpdateDetails={updateWorkflowDetails}
          hasSelectedElements={selectedElements.nodes.length > 0 || selectedElements.edges.length > 0}
          initialName={workflowName}
          initialDescription={workflowDescription}
          workflowId={workflowId}
        />

        {/* Execution Panel */}
        <Panel position="bottom-right" className="bg-background/95 backdrop-blur-sm rounded-md shadow-lg border">
          <ExecutionPanel
            workflowId={workflowId || 'temp-workflow'}
            nodes={nodes}
            edges={edges}
            context={{
              workflowId: workflowId || 'temp-workflow',
              userId: initialWorkflow?.userId || 'temp-user',
              variables: {},
              secrets: {},
              settings: {}
            }}
          />
        </Panel>
      </ReactFlow>
    </div>
      </ExecutionProvider>
  );
  } catch (err) {
    console.error("Error rendering ReactFlow:", err);
    setError(err instanceof Error ? err : new Error("Failed to render workflow canvas"));

    return (
      <div className="flex flex-col items-center justify-center h-screen w-full bg-destructive/10">
        <div className="bg-destructive/10 border border-destructive text-destructive px-6 py-4 rounded-md max-w-md text-center">
          <h2 className="font-semibold mb-2">Error Rendering Workflow</h2>
          <p>{err instanceof Error ? err.message : "An unexpected error occurred"}</p>
          <p className="mt-2 text-sm text-muted-foreground">Please try refreshing the page or contact support if the issue persists.</p>
        </div>
      </div>
    );
  }
  } catch (componentError) {
    console.error("Critical error in WorkflowContainer:", componentError);
    return (
      <div className="flex flex-col items-center justify-center h-screen w-full bg-destructive/10">
        <div className="bg-destructive/10 border border-destructive text-destructive px-6 py-4 rounded-md max-w-md text-center">
          <h2 className="font-semibold mb-2">Critical Error</h2>
          <p>The workflow component encountered a critical error and cannot be displayed.</p>
          <p className="mt-2 text-sm text-muted-foreground">Please refresh the page or contact support.</p>
        </div>
      </div>
    );
  }
}
